# RevenueCat 集成指南

本文档说明如何在 nineCents 应用中配置和使用 RevenueCat 进行内购管理。

## 📋 概述

RevenueCat 是一个强大的内购管理平台，提供了比原生 expo-in-app-purchases 更稳定和功能丰富的解决方案。

### 主要优势
- ✅ 跨平台统一的 API
- ✅ 自动处理收据验证
- ✅ 强大的分析和报告功能
- ✅ 简化的订阅管理
- ✅ 更好的错误处理和重试机制

## 🛠 技术实现

### 核心文件
- `app/utils/revenueCat.ts` - RevenueCat 核心逻辑
- `app/screens/profile.tsx` - 购买界面（已更新）
- `app.json` - 插件配置

### 产品配置
- **产品ID**: `yourae.ninecentspremium`
- **类型**: 一次性购买（Non-consumable）

## 📱 配置步骤

### 1. RevenueCat Dashboard 设置

1. **创建账户**
   - 访问 [RevenueCat Dashboard](https://app.revenuecat.com)
   - 创建新项目

2. **配置应用**
   ```
   - 添加 iOS 应用: Bundle ID: com.yourae.nineCents
   - 添加 Android 应用: Package Name: com.yourae.nineCents
   ```

3. **获取 API Keys**
   ```
   - iOS: appl_xxxxxxxxxx
   - Android: goog_xxxxxxxxxx
   ```

4. **配置产品**
   ```
   - 产品ID: yourae.ninecentspremium
   - 权限标识符: premium (建议使用相同名称)
   ```

### 2. 应用商店配置

#### iOS (App Store Connect)
```
1. 创建 In-App Purchase 产品
   - 产品ID: yourae.ninecentspremium
   - 类型: 非消耗型
   - 价格层级: 根据需要设置

2. 配置沙盒测试账户
   - 创建测试用户
   - 在设备设置中登录沙盒账户
```

#### Android (Google Play Console)
```
1. 创建应用内产品
   - 产品ID: yourae.ninecentspremium
   - 类型: 受管理的产品
   - 价格: 根据需要设置

2. 配置测试账户
   - 添加许可测试人员
   - 上传测试版本
```

### 3. 代码配置

在 `app/utils/revenueCat.ts` 中更新 API Keys：

```typescript
const REVENUECAT_API_KEYS = {
  ios: 'appl_YOUR_ACTUAL_IOS_API_KEY',
  android: 'goog_YOUR_ACTUAL_ANDROID_API_KEY',
};
```

## 🧪 测试指南

### 开发环境测试

1. **模拟模式**
   ```typescript
   // 如果 API Keys 未配置，应用会自动使用模拟模式
   // 在开发环境中可以正常测试 UI 流程
   ```

2. **真实测试**
   ```bash
   # 构建开发版本进行真实测试
   npx expo run:ios
   npx expo run:android
   ```

### 测试检查清单

- [ ] RevenueCat Dashboard 中产品配置正确
- [ ] API Keys 已正确配置
- [ ] 应用商店中产品状态为"准备提交"
- [ ] 沙盒账户购买流程正常
- [ ] 恢复购买功能正常
- [ ] 购买状态持久化正常

## 🔧 故障排除

### 常见问题

1. **初始化失败**
   ```
   问题: RevenueCat 初始化失败
   解决: 检查 API Key 是否正确，网络连接是否正常
   ```

2. **产品未找到**
   ```
   问题: getOfferings 返回空
   解决: 检查 RevenueCat Dashboard 中的产品配置
   ```

3. **购买失败**
   ```
   问题: 购买过程中出错
   解决: 检查应用商店中的产品状态和测试账户
   ```

### 调试技巧

1. **启用详细日志**
   ```typescript
   import { LOG_LEVEL } from 'react-native-purchases';
   Purchases.setLogLevel(LOG_LEVEL.VERBOSE);
   ```

2. **检查用户信息**
   ```typescript
   const customerInfo = await RevenueCat.getCustomerInfo();
   console.log('Customer Info:', customerInfo);
   ```

## 🚀 发布准备

### 发布前检查清单

- [ ] RevenueCat Dashboard 配置完成
- [ ] 应用商店产品配置完成
- [ ] API Keys 已更新为生产环境
- [ ] 测试账户购买流程验证通过
- [ ] 恢复购买功能验证通过

### 生产环境配置

1. **更新 API Keys**
   ```typescript
   // 确保使用生产环境的 API Keys
   const REVENUECAT_API_KEYS = {
     ios: 'appl_PRODUCTION_IOS_KEY',
     android: 'goog_PRODUCTION_ANDROID_KEY',
   };
   ```

2. **移除调试代码**
   ```typescript
   // 移除或注释掉调试日志
   // Purchases.setLogLevel(LOG_LEVEL.DEBUG);
   ```

## 📚 相关文档

- [RevenueCat 官方文档](https://docs.revenuecat.com/)
- [React Native Purchases SDK](https://docs.revenuecat.com/docs/react-native)
- [RevenueCat Dashboard](https://app.revenuecat.com)

## 💡 最佳实践

1. **错误处理**: 始终包装 RevenueCat 调用在 try-catch 中
2. **本地缓存**: 使用 AsyncStorage 缓存购买状态
3. **用户体验**: 提供清晰的加载状态和错误提示
4. **测试覆盖**: 在多个设备和账户上测试购买流程
5. **监控**: 使用 RevenueCat Dashboard 监控购买数据和错误

## 🔄 迁移说明

从 expo-in-app-purchases 迁移到 RevenueCat 的主要变化：

1. **API 变化**
   - `InAppPurchases.purchaseItemAsync()` → `RevenueCat.purchasePackage()`
   - `InAppPurchases.restorePurchases()` → `RevenueCat.restorePurchases()`

2. **状态管理**
   - 不再需要手动设置购买监听器
   - RevenueCat 自动处理收据验证

3. **错误处理**
   - 更统一的错误处理机制
   - 更详细的错误信息

这个迁移提供了更稳定和功能丰富的内购体验，特别适合需要跨平台一致性的应用。
